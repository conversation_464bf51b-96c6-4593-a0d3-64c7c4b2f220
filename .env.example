# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=False
DJANGO_SETTINGS_MODULE=backend.settings_render

# Database (PostgreSQL on Render)
DATABASE_URL=postgresql://username:password@hostname:port/database_name

# Redis (for Channels and Cache)
REDIS_URL=redis://hostname:port/0

# Stripe Payment Settings
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here

# Email Settings (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# AI/ML Settings (Optional)
OPENAI_API_KEY=your-openai-api-key-here
HUGGINGFACE_API_KEY=your-huggingface-api-key-here

# Security Settings (Auto-generated on Render)
ALLOWED_HOSTS=your-app-name.onrender.com
CORS_ALLOWED_ORIGINS=https://your-app-name.onrender.com
CSRF_TRUSTED_ORIGINS=https://your-app-name.onrender.com