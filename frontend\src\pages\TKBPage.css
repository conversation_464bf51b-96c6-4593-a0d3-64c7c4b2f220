/* Responsive <PERSON><PERSON> Header */
.header-btn {
    color: white !important;
    text-decoration: none !important;
    font-size: 14px !important;
    padding: 4px 8px;
}
.table td, .table th {
    padding: 8px 8px !important; /* hoặc 2px nếu muốn sát hơn n<PERSON>a */
}
.dropdown-toggle::after {
    color: white !important;
}
/* Title */
.tkb-title {
    text-transform: none;
    font-size: 18px;
}

/* Table Cell Font Size */
.tkb-cell {
    font-size: 14px;
}


/* Mobile view adjustments */
@media (max-width: 576px) {
    .header-btn {
        font-size: 14px !important;
        padding: 2px 6px !important;
    }

    .tkb-title {
        font-size: 16px !important;
    }

    .tkb-cell {
        font-size: 12px !important;
    }
}
