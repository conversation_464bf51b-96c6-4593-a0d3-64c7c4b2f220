services:
  # Web Service (Django Backend + React Frontend)
  - type: web
    name: ecommerce-web
    env: python
    buildCommand: "./build.sh"
    startCommand: "daphne -b 0.0.0.0 -p $PORT backend.asgi:application"
    plan: starter
    healthCheckPath: /api/health/
    pythonVersion: "3.10.13"
    envVars:
      - key: NODE_VERSION
        value: 18.17.0
      - key: DJANGO_SETTINGS_MODULE
        value: backend.settings_render
      - key: DEBUG
        value: False
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: ecommerce-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: ecommerce-redis
          property: connectionString
      - key: STRIPE_PUBLISHABLE_KEY
        sync: false
      - key: STRIPE_SECRET_KEY
        sync: false
      - key: EMAIL_HOST_USER
        sync: false
      - key: EMAIL_HOST_PASSWORD
        sync: false
      - key: DEFAULT_FROM_EMAIL
        sync: false
      - key: CLOUDINARY_CLOUD_NAME
        sync: false
      - key: CLOUDINARY_API_KEY
        sync: false
      - key: CLOUDINARY_API_SECRET
        sync: false



  # Redis Service
  - type: redis
    name: ecommerce-redis
    plan: starter
    maxmemoryPolicy: allkeys-lru

databases:
  - name: ecommerce-db
    databaseName: ecommerce_prod
    user: ecommerce_user
    plan: starter
