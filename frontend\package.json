{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@stripe/react-stripe-js": "^1.16.5", "@stripe/stripe-js": "^1.47.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^0.27.2", "mdb-react-ui-kit": "^9.0.0", "react": "^18.2.0", "react-bootstrap": "^2.7.2", "react-dom": "^18.2.0", "react-router-bootstrap": "^0.26.2", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "CI=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}