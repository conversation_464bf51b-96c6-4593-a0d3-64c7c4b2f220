import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final int id;
  final String username;
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'isAdmin')
  final bool isAdmin;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name')
  final String? firstName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  final String? lastName;
  final String? phone;
  final String? gender;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'birth_date')
  final String? birthDate;
  final String? address;
  final String? avatar;

  User({
    required this.id,
    required this.username,
    required this.email,
    required this.isAdmin,
    this.firstName,
    this.lastName,
    this.phone,
    this.gender,
    this.birthDate,
    this.address,
    this.avatar,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else if (lastName != null) {
      return lastName!;
    }
    return username;
  }

  User copyWith({
    int? id,
    String? username,
    String? email,
    bool? isAdmin,
    String? firstName,
    String? lastName,
    String? phone,
    String? gender,
    String? birthDate,
    String? address,
    String? avatar,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      isAdmin: isAdmin ?? this.isAdmin,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      address: address ?? this.address,
      avatar: avatar ?? this.avatar,
    );
  }
}

@JsonSerializable()
class AuthTokens {
  final String access;
  final String refresh;

  AuthTokens({
    required this.access,
    required this.refresh,
  });

  factory AuthTokens.fromJson(Map<String, dynamic> json) => _$AuthTokensFromJson(json);
  Map<String, dynamic> toJson() => _$AuthTokensToJson(this);
}

@JsonSerializable()
class LoginRequest {
  final String username;
  final String password;

  LoginRequest({
    required this.username,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) => _$LoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class RegisterRequest {
  final String username;
  final String email;
  final String password;
  @JsonKey(name: 'first_name')
  final String? firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;

  RegisterRequest({
    required this.username,
    required this.email,
    required this.password,
    this.firstName,
    this.lastName,
  });

  factory RegisterRequest.fromJson(Map<String, dynamic> json) => _$RegisterRequestFromJson(json);
  Map<String, dynamic> toJson() => _$RegisterRequestToJson(this);
}
