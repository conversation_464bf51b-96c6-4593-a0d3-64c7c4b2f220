# Hướng dẫn chi tiết chạy dự án với PostgreSQL

## 1. Cài đặt PostgreSQL
1. Tải và cài đặt PostgreSQL từ [trang chủ PostgreSQL](https://www.postgresql.org/download/)
2. Trong quá trình cài đặt, tạo mật khẩu cho người dùng `postgres`
3. <PERSON><PERSON> khi cài đặt, mở pgAdmin hoặc psql để tạo cơ sở dữ liệu mới:
   ```
   CREATE DATABASE ecommerce;
   ```

## 2. Cài đặt môi trường Python
1. <PERSON><PERSON><PERSON> bảo Python đã được cài đặt (phiên bản 3.8 theo Pipfile)
2. Tạo môi trường ảo:
   ```
   python -m venv venv
   ```
3. Kích hoạt môi trường ảo:
   - Windows: `venv\Scripts\activate`
   - Linux/Mac: `source venv/bin/activate`

## 3. <PERSON><PERSON><PERSON> đặt các phụ thuộc
```
pip install -r requirements.txt
```
Hoặc sử dụng Pipfile:
```
pip install pipenv
pipenv install
```

## 4. Tạo file .env
Tạo file `.env` trong thư mục gốc của dự án với nội dung:

```` path=.env mode=EDIT
SECRET_KEY=your_random_secret_key
DEBUG=True
STRIPE_API_KEY=your_stripe_api_key
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/ecommerce
````

Thay thế:
- `your_random_secret_key` bằng một chuỗi ngẫu nhiên
- `your_password` bằng mật khẩu PostgreSQL của bạn
- `your_stripe_api_key` bằng API key từ tài khoản Stripe (nếu cần thanh toán)

## 5. Kiểm tra cấu hình cơ sở dữ liệu
Đảm bảo dòng sau trong `backend/settings.py` không bị comment:

````python path=backend/settings.py mode=EXCERPT
DATABASES = {'default': dj_database_url.config(default=os.environ['DATABASE_URL'], engine='django.db.backends.postgresql')}
````

## 6. Tạo và áp dụng migrations
```
python manage.py makemigrations
python manage.py migrate
```

## 7. Tạo tài khoản admin (tùy chọn)
```
python manage.py createsuperuser
```

## 8. Chạy máy chủ Django
```
python manage.py runserver
```
Ứng dụng sẽ chạy tại `http://127.0.0.1:8000`

## 9. Cài đặt và chạy frontend React
1. Đảm bảo Node.js đã được cài đặt
2. Di chuyển đến thư mục frontend:
   ```
   cd frontend
   ```
3. Cài đặt các phụ thuộc:
   ```
   npm install
   ```
4. Chạy ứng dụng React:
   ```
   npm start
   ```
   Frontend sẽ chạy tại `http://localhost:3000`

## 10. Kiểm tra kết nối
- Truy cập `http://127.0.0.1:8000/admin` để kiểm tra trang admin Django
- Truy cập `http://localhost:3000` để xem giao diện người dùng React

Nếu bạn gặp lỗi kết nối PostgreSQL, hãy kiểm tra:
- Mật khẩu PostgreSQL trong DATABASE_URL
- PostgreSQL đang chạy
- Cơ sở dữ liệu 'ecommerce' đã được tạo


Chạy chat:
Cài redis-server bằng link: https://github.com/tporadowski/redis/releases (giải nén rồi chạy file redis-server để chạy server)
- backend: python manage.py makemigrations chat
           python manage.py migrate
           python manage.py makemigrations 
           python manage.py migrate
           pip install uvicorn
           pip install websockets
           pip install channels
           pip install channels channels_redis
           chạy backend: uvicorn backend.asgi:application (k dùng runserver nữa)
- frontend: npm install socket.io-client
            npm start
            ### Cài đặt AI dependencies:
```
pip install transformers==4.35.0
pip install torch==2.1.0
pip install torchvision==0.16.0
pip install sentence-transformers==2.2.2
pip install scikit-learn==1.3.0
```

### Download AI models (chạy lần đầu - khoảng 1.5GB):
```
python -c "from transformers import CLIPModel, CLIPProcessor; CLIPModel.from_pretrained('openai/clip-vit-base-patch32'); CLIPProcessor.from_pretrained('openai/clip-vit-base-patch32')"
python -c "from sentence_transformers import SentenceTransformer; SentenceTransformer('all-MiniLM-L6-v2')"
```

### Pre-compute embeddings cho tất cả sản phẩm (tăng tốc độ search):
```
python manage.py precompute_embeddings
```

### Chạy với AI Search:
- Backend: `python manage.py runserver` (hoặc uvicorn cho chat)
- Frontend: `npm start`

### Tính năng AI Search:
- Tìm kiếm bằng hình ảnh: Upload ảnh để tìm sản phẩm tương tự
- Tìm kiếm bằng mô tả: Mô tả sản phẩm bằng ngôn ngữ tự nhiên
- Tìm kiếm kết hợp: Sử dụng cả text và hình ảnh

### Lưu ý:
- Models sẽ tải về ~1.5GB lần đầu
- Cache embeddings sẽ lưu trong thư mục `ai_cache/`
- Có thể xóa cache để recompute: `rm -rf ai_cache/`