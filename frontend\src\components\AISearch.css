.ai-result-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  border-radius: 12px;
}

.ai-result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.ai-result-card img {
  transition: transform 0.3s ease;
}

.ai-result-card:hover img {
  transform: scale(1.08);
}

/* Enhanced Compatibility Badge */
.compatibility-badge {
  font-size: 0.8rem;
  font-weight: 700;
  border-radius: 20px;
  padding: 6px 12px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255,255,255,0.3);
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Compatibility Progress Section */
.compatibility-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  border: 1px solid #dee2e6;
}

.compatibility-score {
  font-size: 1.1rem;
  font-weight: 800;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.compatibility-label {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Progress Bar */
.compatibility-progress {
  height: 8px;
  border-radius: 10px;
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
  overflow: hidden;
}

.compatibility-bar-animated {
  border-radius: 10px;
  transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.compatibility-bar-animated::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Color variants for compatibility */
.compatibility-excellent {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.compatibility-good {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: #333;
}

.compatibility-fair {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
  color: white;
}

.compatibility-poor {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
}

/* Text colors for scores */
.text-excellent {
  color: #28a745 !important;
  text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2);
}

.text-good {
  color: #ffc107 !important;
  text-shadow: 0 1px 2px rgba(255, 193, 7, 0.2);
}

.text-fair {
  color: #17a2b8 !important;
  text-shadow: 0 1px 2px rgba(23, 162, 184, 0.2);
}

.text-poor {
  color: #6c757d !important;
  text-shadow: 0 1px 2px rgba(108, 117, 125, 0.2);
}

/* AI Search Button */
.ai-search-btn {
  background: linear-gradient(45deg, #ffffff, #e0e0e0) !important; /* nền trắng - xám nhạt */
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  font-weight: 600;
  color: #000; /* chữ/icon đen */
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1); /* bóng đen nhạt */
}

.ai-search-btn:hover {
  background: linear-gradient(45deg, #555555, #000000) !important; /* nền xám đậm - đen */
  color: #fff; /* chữ/icon trắng khi hover */
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.ai-search-btn i {
  filter: grayscale(100%) !important; /* icon thành trắng đen */
}


/* Modal enhancements */
.modal-header {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border-radius: 0.5rem 0.5rem 0 0;
}

.modal-header .btn-close {
  filter: invert(1);
}

/* Search type tabs */
.search-type-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  padding: 4px;
  background: #f8f9fa;
  border-radius: 12px;
}

.search-type-tab {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  color: #6c757d;
}

.search-type-tab.active {
  background: white;
  color: #007bff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-type-tab:hover:not(.active) {
  background: rgba(255,255,255,0.5);
  color: #495057;
}

/* Results header */
.results-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.results-header h6 {
  margin: 0;
  color: #495057;
  font-weight: 700;
}

/* Loading animation */
.ai-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.ai-loading .spinner-border {
  width: 3rem;
  height: 3rem;
  border-width: 0.3em;
}

/* Image preview */
.image-preview {
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 8px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.image-preview:hover {
  border-color: #007bff;
  background: #e7f3ff;
}

.image-preview img {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Form controls */
.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

/* Responsive */
@media (max-width: 768px) {
  .compatibility-section {
    padding: 8px;
  }
  
  .compatibility-score {
    font-size: 1rem;
  }
  
  .compatibility-badge {
    font-size: 0.7rem;
    padding: 4px 8px;
  }
  
  .search-type-tabs {
    flex-direction: column;
  }
  
  .ai-result-card:hover {
    transform: none;
  }
}

/* Paste instructions */
.paste-instructions .alert {
  border-radius: 12px;
  border: none;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  color: #1565c0;
}

.paste-instructions kbd {
  background: #1565c0;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.85em;
}

/* Enhanced image upload area */
.image-upload-area {
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  transition: all 0.3s ease;
  position: relative;
  min-height: 150px;
}

.image-upload-area.drag-over {
  border-color: #007bff;
  background: #e7f3ff;
  transform: scale(1.02);
}

.upload-label {
  cursor: pointer;
  display: block;
  width: 100%;
  height: 100%;
  margin: 0;
}

.upload-content {
  color: #6c757d;
  padding: 20px;
}

.upload-content:hover {
  color: #007bff;
}

/* Image preview container */
.image-preview-container {
  position: relative;
  display: inline-block;
}

.preview-image {
  max-width: 200px;
  max-height: 200px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.remove-image-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(220,53,69,0.3);
}

.remove-image-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* Drag overlay */
.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,123,255,0.1);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 12px;
}

.drag-content {
  text-align: center;
  color: #007bff;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,123,255,0.2);
}


